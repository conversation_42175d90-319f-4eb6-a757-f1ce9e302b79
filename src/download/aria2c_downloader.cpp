#include "download/aria2c_downloader.h"
#include "utilities/logger.h"
#include "utilities/file_utils.h"
#include "utilities/string_utils.h"
#include <fstream>
#include <sstream>
#include <chrono>
#include <cstdlib>
#include <regex>

namespace pixiv_downloader {
namespace download {

Aria2cDownloader::Aria2cDownloader(const std::string& aria2c_path, const std::string& default_options)
    : aria2c_path_(aria2c_path), default_options_(default_options) {
}

Aria2cDownloader::~Aria2cDownloader() = default;

bool Aria2cDownloader::IsAvailable() const {
    // 尝试执行aria2c --version来检查是否可用
    std::string command = EscapeShellArg(aria2c_path_) + " --version 2>/dev/null";
    int result = std::system(command.c_str());
    return result == 0;
}

Aria2cResult Aria2cDownloader::DownloadFile(const Aria2cTask& task) {
    return DownloadFiles({task})[0];
}

std::vector<Aria2cResult> Aria2cDownloader::DownloadFiles(const std::vector<Aria2cTask>& tasks) {
    std::vector<Aria2cResult> results;
    
    if (tasks.empty()) {
        return results;
    }

    try {
        // 创建输入文件
        std::string input_file = CreateInputFile(tasks);
        if (input_file.empty()) {
            LOG_ERROR("创建aria2c输入文件失败");
            for (size_t i = 0; i < tasks.size(); ++i) {
                Aria2cResult result;
                result.success = false;
                result.error_message = "创建输入文件失败";
                results.push_back(result);
            }
            return results;
        }

        // 获取输出目录
        std::string output_dir = utilities::FileUtils::GetDirectoryPath(tasks[0].output_path);
        
        // 执行aria2c
        bool success = ExecuteAria2c(input_file, output_dir);
        
        // 清理临时文件
        CleanupTempFile(input_file);
        
        // 生成结果
        for (size_t i = 0; i < tasks.size(); ++i) {
            Aria2cResult result;
            result.success = success && utilities::FileUtils::FileExists(tasks[i].output_path);
            if (result.success) {
                result.file_size = utilities::FileUtils::GetFileSize(tasks[i].output_path);
                LOG_DEBUG("Aria2c下载成功: {}", tasks[i].output_path);
            } else {
                result.error_message = "Aria2c下载失败";
                LOG_ERROR("Aria2c下载失败: {}", tasks[i].url);
            }
            results.push_back(result);
        }
        
    } catch (const std::exception& e) {
        LOG_ERROR("Aria2c下载过程中发生异常: {}", e.what());
        for (size_t i = 0; i < tasks.size(); ++i) {
            Aria2cResult result;
            result.success = false;
            result.error_message = e.what();
            results.push_back(result);
        }
    }

    return results;
}

void Aria2cDownloader::SetProgressCallback(std::function<void(const std::string&, double)> callback) {
    progress_callback_ = callback;
}

void Aria2cDownloader::SetOptions(const std::string& options) {
    default_options_ = options;
}

std::string Aria2cDownloader::GetVersion() const {
    std::string command = EscapeShellArg(aria2c_path_) + " --version 2>/dev/null";
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        return "";
    }

    std::string result;
    char buffer[128];
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        result += buffer;
    }
    pclose(pipe);

    // 提取版本号
    std::regex version_regex(R"(aria2 version (\d+\.\d+\.\d+))");
    std::smatch match;
    if (std::regex_search(result, match, version_regex)) {
        return match[1].str();
    }

    return result.empty() ? "未知" : utilities::StringUtils::Trim(result);
}

std::string Aria2cDownloader::CreateInputFile(const std::vector<Aria2cTask>& tasks) {
    try {
        // 创建临时文件
        std::string temp_dir = utilities::FileUtils::GetTempDirectory();
        std::string input_file = utilities::FileUtils::JoinPath(temp_dir, "aria2c_input_" + 
                                std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()) + ".txt");

        std::ofstream file(input_file);
        if (!file.is_open()) {
            LOG_ERROR("无法创建aria2c输入文件: {}", input_file);
            return "";
        }

        for (const auto& task : tasks) {
            // 写入URL
            file << task.url << std::endl;
            
            // 写入输出路径
            file << " out=" << utilities::FileUtils::GetFileName(task.output_path) << std::endl;
            
            // 写入目录
            std::string dir = utilities::FileUtils::GetDirectoryPath(task.output_path);
            file << " dir=" << dir << std::endl;
            
            // 写入Referer
            if (!task.referer.empty()) {
                file << " referer=" << task.referer << std::endl;
            }
            
            // 写入User-Agent
            if (!task.user_agent.empty()) {
                file << " user-agent=" << task.user_agent << std::endl;
            }
            
            // 写入Cookie
            if (!task.cookie.empty()) {
                file << " header=Cookie: " << task.cookie << std::endl;
            }
            
            // 写入其他头部
            for (const auto& [name, value] : task.headers) {
                file << " header=" << name << ": " << value << std::endl;
            }
            
            file << std::endl; // 任务分隔符
        }

        file.close();
        LOG_DEBUG("创建aria2c输入文件成功: {}", input_file);
        return input_file;
        
    } catch (const std::exception& e) {
        LOG_ERROR("创建aria2c输入文件时发生异常: {}", e.what());
        return "";
    }
}

bool Aria2cDownloader::ExecuteAria2c(const std::string& input_file, const std::string& output_dir) {
    try {
        // 构建命令
        std::ostringstream command;
        command << EscapeShellArg(aria2c_path_);
        command << " --input-file=" << EscapeShellArg(input_file);
        
        // 添加默认选项
        if (!default_options_.empty()) {
            command << " " << default_options_;
        }
        
        // 添加其他选项
        command << " --continue=true";
        command << " --max-connection-per-server=4";
        command << " --split=4";
        command << " --min-split-size=1M";
        command << " --max-concurrent-downloads=1";
        command << " --check-certificate=false";
        command << " --summary-interval=0";
        command << " --download-result=hide";
        
        std::string cmd = command.str();
        LOG_DEBUG("执行aria2c命令: {}", cmd);
        
        // 执行命令
        int result = std::system(cmd.c_str());
        
        if (result == 0) {
            LOG_DEBUG("Aria2c执行成功");
            return true;
        } else {
            LOG_ERROR("Aria2c执行失败，退出代码: {}", result);
            return false;
        }
        
    } catch (const std::exception& e) {
        LOG_ERROR("执行aria2c时发生异常: {}", e.what());
        return false;
    }
}

std::vector<Aria2cResult> Aria2cDownloader::ParseOutput(const std::string& output) {
    std::vector<Aria2cResult> results;
    // 这里可以解析aria2c的输出来获取更详细的结果
    // 目前简化实现
    (void)output; // 避免未使用参数警告
    return results;
}

void Aria2cDownloader::CleanupTempFile(const std::string& file_path) {
    if (!file_path.empty() && utilities::FileUtils::FileExists(file_path)) {
        utilities::FileUtils::DeleteFile(file_path);
        LOG_DEBUG("清理临时文件: {}", file_path);
    }
}

std::string Aria2cDownloader::EscapeShellArg(const std::string& arg) const {
    if (arg.empty()) {
        return "''";
    }
    
    // 简单的shell转义实现
    std::string escaped = arg;
    escaped = utilities::StringUtils::ReplaceAll(escaped, "'", "'\"'\"'");
    return "'" + escaped + "'";
}

} // namespace download
} // namespace pixiv_downloader
